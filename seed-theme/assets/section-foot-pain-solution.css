/* Foot Pain Solution Section Styles */
.foot-pain-section {
  width: 100%;
  padding: 0px 0;
  background-color: rgb(247, 250, 253);
  overflow: hidden;
}

.foot-pain-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.foot-pain-content {
  display: flex;
  align-items: center;
  gap: 100px;
  min-height: 400px;
  width: 100%;
}

.foot-pain-image {
  flex: 0 0 50%;
  width: 50%;
  height: 379px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.foot-pain-image img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 620px;
  height: auto;
  object-fit: contain;
  object-position: center center;
  border: none;
  outline: none;
  transition: opacity 0.3s ease;
  will-change: transform;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  opacity: 1;
  vertical-align: middle;
}

.foot-pain-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #999;
  font-family: Lato, sans-serif;
  font-size: 14px;
}

.foot-pain-image-placeholder svg {
  margin-bottom: 10px;
}

.foot-pain-text {
  flex: 0 0 50%;
  width: 50%;
  padding-left: 15px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.foot-pain-title {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Merriweather, serif;
  font-size: 36px;
  font-weight: 700;
  line-height: 50.4px;
  letter-spacing: 0.12px;
  margin: 0 0 15px 0;
  width: 100%;
  max-width: 400px;
  unicode-bidi: isolate;
}

.foot-pain-description {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  font-family: Lato, sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  margin-bottom: 20px;
  width: 100%;
  max-width: 400px;
}

.foot-pain-description p {
  margin: 0 0 10px 0;
}

.foot-pain-features {
  list-style: none;
  padding: 0;
  margin: 0 0 25px 0;
  width: 100%;
  max-width: 400px;
}

.foot-pain-feature-item {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: list-item;
  font-family: Lato, sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  list-style: none;
  padding: 0 0 0 25px;
  position: relative;
  text-align: left;
  width: 100%;
  max-width: 400px;
  unicode-bidi: isolate;
  margin-bottom: 8px;
}

.foot-pain-feature-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 6px;
  width: 12px;
  height: 12px;
  border: 2px solid rgb(74, 158, 218);
  border-radius: 50%;
  box-sizing: border-box;
  display: block;
}

.foot-pain-button-container {
  margin-top: 15px;
  width: 100%;
  max-width: 400px;
}

/* Button styles - updated to match risk-free-trial */
.foot-pain-btn {
  appearance: button;
  background-color: rgb(0, 158, 224);
  border: 1px solid rgb(0, 158, 224);
  border-radius: 4px;
  box-shadow: rgba(0, 158, 224, 0.4) 0px 4px 16px 0px;
  box-sizing: border-box;
  color: rgb(255, 255, 255);
  cursor: pointer;
  display: inline-block;
  font-family: Lato, sans-serif;
  font-size: 16px;
  font-weight: 600;
  height: 50px;
  letter-spacing: 0.571429px;
  line-height: 24px;
  min-width: 200px;
  padding: 12px 30px;
  position: relative;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.2s linear;
  vertical-align: middle;
  width: 200px;
  white-space: nowrap;
  outline: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.foot-pain-btn:hover {
  background-color: rgb(20, 178, 244);
  border-color: rgb(20, 178, 244);
  color: rgb(255, 255, 255);
  text-decoration: none;
}

.foot-pain-btn:focus {
  outline: 2px solid rgb(0, 158, 224);
  outline-offset: 2px;
}

.foot-pain-btn:active {
  background-color: rgb(0, 138, 196);
  border-color: rgb(0, 138, 196);
}

.foot-pain-btn[aria-disabled="true"] {
  background-color: #ccc;
  border-color: #ccc;
  color: #666;
  cursor: not-allowed;
}

/* Guarantee text - copied from comparison section */
.foot-pain-guarantee {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Lato, sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 22.4px;
  margin: 12px 0 0 0;
  max-width: 230px;
  text-align: left;
  unicode-bidi: isolate;
  width: auto;
}

/* Large Desktop */
@media (min-width: 1440px) {
  .foot-pain-container {
    max-width: 1400px;
  }
  
  .foot-pain-content {
    min-height: 450px;
  }
  
  .foot-pain-image {
    height: 420px;
  }
  
  .foot-pain-title {
    font-size: 42px;
    line-height: 56px;
  }
  
  .foot-pain-description {
    font-size: 18px;
    line-height: 26px;
  }
}

/* Desktop */
@media (max-width: 1439px) and (min-width: 1025px) {
  .foot-pain-container {
    max-width: 1200px;
  }
}

/* Tablet Landscape */
@media (max-width: 1024px) and (min-width: 769px) {

  .foot-pain-content {
    min-height: 350px;
  }

  .foot-pain-image {
    height: 320px;
  }

  .foot-pain-title {
    font-size: 32px;
    line-height: 44px;
  }

  .foot-pain-description {
    font-size: 15px;
    line-height: 21px;
  }

  .foot-pain-feature-item {
    font-size: 15px;
    line-height: 21px;
  }
}

/* Tablet Portrait */
@media (max-width: 768px) and (min-width: 481px) {
  .foot-pain-content {
    flex-direction: column;
    min-height: auto;
    gap: 30px;
  }

  .foot-pain-image {
    flex: none;
    width: 100%;
    height: 300px;
    order: 1;
  }

  .foot-pain-text {
    flex: none;
    width: 100%;
    padding: 0;
    text-align: center;
    order: 2;
  }

  .foot-pain-title {
    font-size: 28px;
    line-height: 38px;
    max-width: none;
  }

  .foot-pain-description {
    max-width: none;
  }

  .foot-pain-features {
    max-width: none;
    text-align: left;
    display: inline-block;
  }

  .foot-pain-feature-item {
    max-width: none;
  }

  .foot-pain-btn {
    min-width: 200px;
    width: 200px;
  }

  .foot-pain-guarantee {
    max-width: none;
    text-align: center;
  }
}

/* Mobile */
@media (max-width: 480px) {
  .foot-pain-section {
    padding: 0px 0;
  }

  .foot-pain-content {
    flex-direction: column;
    min-height: auto;
    gap: 25px;
  }

  .foot-pain-image {
    flex: none;
    width: 100%;
    height: 250px;
    order: 1;
  }

  .foot-pain-text {
    flex: none;
    width: 100%;
    padding: 0;
    text-align: center;
    order: 2;
  }

  .foot-pain-title {
    font-size: 24px;
    line-height: 32px;
    max-width: none;
  }

  .foot-pain-description {
    font-size: 15px;
    line-height: 21px;
    max-width: none;
  }

  .foot-pain-features {
    max-width: none;
    text-align: left;
    display: inline-block;
  }

  .foot-pain-feature-item {
    font-size: 15px;
    line-height: 21px;
    max-width: none;
  }

  .foot-pain-btn {
    min-width: 180px;
    width: 180px;
    font-size: 14px;
    padding: 10px 20px;
    height: 44px;
  }

  .foot-pain-guarantee {
    font-size: 14px;
    line-height: 20px;
    max-width: none;
    text-align: center;
  }
}

/* Performance optimizations */
.foot-pain-section {
  contain: layout style paint;
  will-change: auto;
}

.foot-pain-image {
  contain: layout style paint;
}

.foot-pain-image img {
  transform-origin: center center;
}

/* WebP support optimization */
@supports (image-rendering: -webkit-optimize-contrast) {
  .foot-pain-image img {
    image-rendering: -webkit-optimize-contrast;
  }
}

@supports (image-rendering: crisp-edges) {
  .foot-pain-image img {
    image-rendering: crisp-edges;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .foot-pain-btn,
  .foot-pain-image img {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .foot-pain-title {
    color: #000000;
  }

  .foot-pain-description {
    color: #000000;
  }

  .foot-pain-feature-item {
    color: #000000;
  }

  .foot-pain-btn {
    border-width: 3px;
  }

  .foot-pain-feature-item::before {
    border-width: 3px;
  }
}
