{% comment %}
  Section: Professionals Behind Custom Orthotics
  Description: Showcases the professional team with customizable content
{% endcomment %}

<section class="shopify-section-professionals">
  <div class="professionals-container">
    {%- if section.settings.title != blank -%}
      <h2 class="professionals-title">{{ section.settings.title }}</h2>
    {%- endif -%}
    
    <div class="professionals-content">
      <div class="professional-image-container">
        {%- if section.settings.professional_image != blank -%}
          <img src="{{ section.settings.professional_image | image_url: width: 590 }}" 
               alt="{{ section.settings.professional_name | default: 'Professional' }}"
               class="professional-image"
               width="500" 
               height="431">
        {%- else -%}
          <div class="professional-image-placeholder"></div>
        {%- endif -%}
        
        <div class="professional-info">
          {%- if section.settings.professional_name != blank -%}
            <p class="professional-name">{{ section.settings.professional_name }}</p>
          {%- endif -%}
          {%- if section.settings.professional_title != blank -%}
            <p class="professional-title">{{ section.settings.professional_title }}</p>
          {%- endif -%}
        </div>
      </div>
      
      <div class="professional-text-container">
        {%- if section.settings.description_1 != blank -%}
          <p class="professional-description-1">{{ section.settings.description_1 }}</p>
        {%- endif -%}
        
        {%- if section.settings.description_2 != blank -%}
          <p class="professional-description-2">{{ section.settings.description_2 }}</p>
        {%- endif -%}
      </div>
    </div>
  </div>
</section>

<style>
  .shopify-section-professionals {
    background: transparent;
    padding: 50px 20px;
  }

  .shopify-section-professionals .professionals-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .shopify-section-professionals .professionals-title {
    width: 579px;
    color: #4A4A4A;
    text-align: center;
    font-family: Merriweather, serif;
    font-size: 36px;
    font-style: normal;
    font-weight: 700;
    line-height: 57px;
    letter-spacing: 0.2px;
    margin: 0 auto 60px auto;
  }

  .shopify-section-professionals .professionals-content {
    display: flex;
    align-items: center;
    gap: 60px;
    justify-content: center;
  }

  .shopify-section-professionals .professional-image-container {
    flex-shrink: 0;
  }

  .shopify-section-professionals .professional-image,
  .shopify-section-professionals .professional-image-placeholder {
    width: 500px;
    height: 431px;
    border-radius: 8px;
    object-fit: cover;
  }

  .shopify-section-professionals .professional-image-placeholder {
    background: #BEEBFC;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .shopify-section-professionals .professional-info {
    text-align: center;
  }

  .shopify-section-professionals .professional-name {
    color: #6e6e6e;
    text-align: center;
    font-family: Merriweather, serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.1px;
    margin: 0 0 2px 0;
  }

  .shopify-section-professionals .professional-title {
    color: #6e6e6e;
    text-align: center;
    font-family: Merriweather, serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.1px;
    margin: 0;
  }

  .shopify-section-professionals .professional-text-container {
    margin-bottom: 20px;
    flex: 1;
    max-width: 548px;
  }

  .shopify-section-professionals .professional-description-1 {
    width: 548px;
    color: #808080;
    font-family: Lato, sans-serif;
    font-size: 22px;
    font-style: normal;
    font-weight: 400;
    line-height: 33px;
    letter-spacing: 0.2px;
    margin: 0 0 10px 0;
  }

  .shopify-section-professionals .professional-description-2 {
    width: 527px;
    color: #37A1EE;
    font-family: Lato, sans-serif;
    font-size: 22px;
    font-style: normal;
    font-weight: 700;
    line-height: 33px;
    letter-spacing: 0.13px;
    margin: 0;
  }

  /* Mobile Responsive */
  @media (max-width: 1200px) {
    .shopify-section-professionals .professionals-title {
      width: 100%;
      font-size: 32px;
      line-height: 44px;
      margin-bottom: 40px;
    }

    .shopify-section-professionals .professionals-content {
      flex-direction: column;
      align-items: center;
      gap: 40px;
    }

    .shopify-section-professionals .professional-image,
    .shopify-section-professionals .professional-image-placeholder {
      width: 100%;
      max-width: 500px;
      height: auto;
      aspect-ratio: 590 / 431;
    }

    .shopify-section-professionals .professional-description-1,
    .shopify-section-professionals .professional-description-2 {
      width: 100%;
      max-width: 548px;
    }
  }

  @media (max-width: 768px) {
    .shopify-section-professionals {
      padding: 50px 20px;
    }

    .shopify-section-professionals .professionals-title {
      font-size: 28px;
      line-height: 38px;
      margin-bottom: 30px;
    }

    .shopify-section-professionals .professional-description-1,
    .shopify-section-professionals .professional-description-2 {
      font-size: 18px;
      line-height: 32px;
    }

    .shopify-section-professionals .professional-description-1 {
      margin-bottom: 20px;
    }
  }
</style>

{% schema %}
{
  "name": "Professionals Section",
  "tag": "section",
  "class": "section-professionals",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "The professionals behind your custom orthotics"
    },
    {
      "type": "image_picker",
      "id": "professional_image",
      "label": "Professional Image"
    },
    {
      "type": "text",
      "id": "professional_name",
      "label": "Professional Name",
      "default": "Philip Wells, Podiatrist,"
    },
    {
      "type": "text",
      "id": "professional_title",
      "label": "Professional Title",
      "default": "Chief Medical Officer at Upstep"
    },
    {
      "type": "textarea",
      "id": "description_1",
      "label": "First Description",
      "default": "Each pair of Upsteps is designed and produced under the watchful eye of our head of podiatrist, Philip Wells, and his team of international-level professionals."
    },
    {
      "type": "textarea",
      "id": "description_2",
      "label": "Second Description (Highlighted)",
      "default": "With over 80K inserts and 20 years of work, we developed the next step in custom orthotics. Our innovative custom orthotics are made from the world's most advanced materials in our cutting-edge orthotic facility."
    }
  ],
  "presets": [
    {
      "name": "Professionals Section"
    }
  ]
}
{% endschema %}
