{%- assign current_variant = product.selected_or_first_available_variant -%}
{%- if current_variant and section.settings.enable_klarna -%}
  <div class="klarna-payment-section" id="klarna-section-{{ section.id }}">
    <div class="klarna-badge-container">
      {%- if section.settings.custom_svg != blank -%}
        <div class="klarna-logo-custom">{{ section.settings.custom_svg }}</div>
      {%- else -%}
        <div class="klarna-logo-default" style="background: none; padding: 0; display: flex; align-items: center;">
          <svg aria-label="osm-klarna-title" width="70" height="30" viewBox="0 0 71.25 30" version="2.1" xmlns="http://www.w3.org/2000/svg" style="display: block;">
            <g clip-path="url(#a)">
              <path fill="#FFA8CD" d="M62.769 0H8.48A8.48 8.48 0 0 0 0 8.481V21.52A8.48 8.48 0 0 0 8.481 30H62.77a8.48 8.48 0 0 0 8.481-8.481V8.48A8.48 8.48 0 0 0 62.769 0"/>
              <path fill="#0B051D" d="M57.412 19.142c-1.244 0-2.213-1.029-2.213-2.278s.97-2.277 2.213-2.277 2.214 1.028 2.214 2.277-.97 2.278-2.214 2.278m-.621 2.406c1.06 0 2.414-.404 3.164-1.984l.073.037c-.329.863-.329 1.378-.329 1.506v.202h2.67v-8.89H59.7v.202c0 .129 0 .643.33 1.506l-.074.037c-.75-1.58-2.104-1.984-3.164-1.984-2.543 0-4.336 2.02-4.336 4.684s1.793 4.684 4.336 4.684m-8.983-9.368c-1.207 0-2.158.423-2.926 1.984l-.074-.037c.33-.863.33-1.377.33-1.506v-.202h-2.671v8.89h2.744v-4.684c0-1.23.713-2.002 1.866-2.002 1.152 0 1.719.662 1.719 1.984v4.702h2.744v-5.657c0-2.02-1.573-3.472-3.732-3.472m-9.31 1.984-.074-.037c.33-.863.33-1.377.33-1.506v-.202h-2.671v8.89h2.743l.019-4.28c0-1.248.658-2.002 1.738-2.002.292 0 .53.037.804.11V12.42c-1.207-.257-2.286.202-2.89 1.745m-8.727 4.978c-1.244 0-2.213-1.029-2.213-2.278s.97-2.277 2.213-2.277 2.214 1.028 2.214 2.277-.97 2.278-2.214 2.278m-.622 2.406c1.061 0 2.415-.404 3.165-1.984l.073.037c-.329.863-.329 1.378-.329 1.506v.202h2.67v-8.89h-2.67v.202c0 .129 0 .643.33 1.506l-.074.037c-.75-1.58-2.104-1.984-3.165-1.984-2.542 0-4.335 2.02-4.335 4.684s1.793 4.684 4.335 4.684m-8.158-.239h2.744V8.452H20.99zM18.978 8.452H16.18c0 2.296-1.409 4.353-3.55 5.822l-.84.588v-6.41H8.88v12.857h2.91v-6.373l4.81 6.373h3.55l-4.629-6.098c2.104-1.524 3.476-3.894 3.457-6.76"/>
            </g>
            <defs>
              <clipPath id="a">
                <path fill="#fff" d="M0 0h71.25v30H0z"/>
              </clipPath>
            </defs>
          </svg>
        </div>
      {%- endif -%}
      
      <div class="klarna-payment-text">
        <span class="klarna-payment-amount">4 interest-free payments of ${{ current_variant.price | divided_by: 4 | money_without_currency }}</span>
        <a href="#" class="klarna-learn-more" data-klarna-popup="{{ section.id }}">Learn more</a>
      </div>
    </div>
  </div>

  {%- comment -%} Klarna Popup {%- endcomment -%}
  <div class="klarna-popup-overlay" id="klarna-popup-{{ section.id }}" style="display: none;">
    <div class="klarna-popup">
      <div class="klarna-popup-header">
        <div class="klarna-popup-logo" style="background: none; padding: 0; display: flex; align-items: center; justify-content: center;">
          {%- if section.settings.popup_header_svg != blank -%}
            {{ section.settings.popup_header_svg }}
          {%- else -%}
            <svg aria-label="osm-klarna-title" width="70" height="30" viewBox="0 0 71.25 30" version="2.1" xmlns="http://www.w3.org/2000/svg" style="display: block;">
              <g clip-path="url(#c)">
                <path fill="#FFA8CD" d="M62.769 0H8.48A8.48 8.48 0 0 0 0 8.481V21.52A8.48 8.48 0 0 0 8.481 30H62.77a8.48 8.48 0 0 0 8.481-8.481V8.48A8.48 8.48 0 0 0 62.769 0"/>
                <path fill="#0B051D" d="M57.412 19.142c-1.244 0-2.213-1.029-2.213-2.278s.97-2.277 2.213-2.277 2.214 1.028 2.214 2.277-.97 2.278-2.214 2.278m-.621 2.406c1.06 0 2.414-.404 3.164-1.984l.073.037c-.329.863-.329 1.378-.329 1.506v.202h2.67v-8.89H59.7v.202c0 .129 0 .643.33 1.506l-.074.037c-.75-1.58-2.104-1.984-3.164-1.984-2.543 0-4.336 2.02-4.336 4.684s1.793 4.684 4.336 4.684m-8.983-9.368c-1.207 0-2.158.423-2.926 1.984l-.074-.037c.33-.863.33-1.377.33-1.506v-.202h-2.671v8.89h2.744v-4.684c0-1.23.713-2.002 1.866-2.002 1.152 0 1.719.662 1.719 1.984v4.702h2.744v-5.657c0-2.02-1.573-3.472-3.732-3.472m-9.31 1.984-.074-.037c.33-.863.33-1.377.33-1.506v-.202h-2.671v8.89h2.743l.019-4.28c0-1.248.658-2.002 1.738-2.002.292 0 .53.037.804.11V12.42c-1.207-.257-2.286.202-2.89 1.745m-8.727 4.978c-1.244 0-2.213-1.029-2.213-2.278s.97-2.277 2.213-2.277 2.214 1.028 2.214 2.277-.97 2.278-2.214 2.278m-.622 2.406c1.061 0 2.415-.404 3.165-1.984l.073.037c-.329.863-.329 1.378-.329 1.506v.202h2.67v-8.89h-2.67v.202c0 .129 0 .643.33 1.506l-.074.037c-.75-1.58-2.104-1.984-3.165-1.984-2.542 0-4.335 2.02-4.335 4.684s1.793 4.684 4.335 4.684m-8.158-.239h2.744V8.452H20.99zM18.978 8.452H16.18c0 2.296-1.409 4.353-3.55 5.822l-.84.588v-6.41H8.88v12.857h2.91v-6.373l4.81 6.373h3.55l-4.629-6.098c2.104-1.524 3.476-3.894 3.457-6.76"/>
              </g>
              <defs>
                <clipPath id="c">
                  <path fill="#fff" d="M0 0h71.25v30H0z"/>
                </clipPath>
              </defs>
            </svg>
          {%- endif -%}
        </div>
        <button class="klarna-popup-close" data-klarna-close="{{ section.id }}">&times;</button>
      </div>
      
      <div class="klarna-popup-content">
        <h2 class="klarna-popup-main-title">{{ section.settings.popup_title | default: "Buy now. Pay with Klarna at your own pace." }}</h2>
        <p class="klarna-popup-subtitle">{{ section.settings.popup_subtitle | default: "Get what you love, choose how you pay." }}</p>
        
        <div class="klarna-payment-options">
          <div class="klarna-option klarna-pay-full">
            <div class="klarna-option-price">${{ current_variant.price | money_without_currency }}</div>
            <div class="klarna-option-label">Pay now</div>
            <div class="klarna-option-badge">Pay in full</div>
          </div>
          
          <div class="klarna-option klarna-pay-installments">
            <div class="klarna-option-price">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
            <div class="klarna-option-label">Every 14 days</div>
            <div class="klarna-option-badge">Pay in 4</div>
            
            <div class="klarna-installment-schedule">
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-1"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">Today</div>
                </div>
              </div>
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-2"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">In 2 weeks</div>
                </div>
              </div>
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-3"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">In 4 weeks</div>
                </div>
              </div>
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-4"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">In 6 weeks</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="klarna-summary">
          <div class="klarna-summary-item">
            <span class="klarna-summary-label">APR</span>
            <span class="klarna-summary-value">0%</span>
          </div>
          <div class="klarna-summary-item">
            <span class="klarna-summary-label">Total interest</span>
            <span class="klarna-summary-value">Free</span>
          </div>
          <div class="klarna-summary-item">
            <span class="klarna-summary-label">Total</span>
            <span class="klarna-summary-value">${{ current_variant.price | money_without_currency }}</span>
          </div>
        </div>
        
        <p class="klarna-disclaimer">{{ section.settings.disclaimer | default: "For Pay in 4, CA resident loans made or arranged pursuant to California financing law license. NMLS # 1353190." }}</p>
        
        <div class="klarna-how-it-works">
          <h3>How it works</h3>
          <div class="klarna-step">
            <div class="klarna-step-number">1</div>
            <div class="klarna-step-content">
              <h4>At checkout select Klarna</h4>
            </div>
          </div>
          <div class="klarna-step">
            <div class="klarna-step-number">2</div>
            <div class="klarna-step-content">
              <h4>Choose your payment plan</h4>
              <p>Different payment plans may be shown depending on the purchase amount and credit score.</p>
            </div>
          </div>
          <div class="klarna-step">
            <div class="klarna-step-number">3</div>
            <div class="klarna-step-content">
              <h4>Complete your checkout</h4>
              <p>The amount will be charged based on the payment plan you chose.</p>
            </div>
          </div>
        </div>
        
        <button class="klarna-got-it-btn" data-klarna-close="{{ section.id }}">Got it</button>
      </div>
    </div>
  </div>
{%- endif -%}

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Open popup
  const learnMoreBtn = document.querySelector('[data-klarna-popup="{{ section.id }}"]');
  const popup = document.getElementById('klarna-popup-{{ section.id }}');
  
  if (learnMoreBtn && popup) {
    learnMoreBtn.addEventListener('click', function(e) {
      e.preventDefault();
      popup.style.display = 'flex';
      document.body.style.overflow = 'hidden';
      document.body.classList.add('klarna-popup-open');
    });
  }
  
  // Close popup
  const closeButtons = document.querySelectorAll('[data-klarna-close="{{ section.id }}"]');
  closeButtons.forEach(function(btn) {
    btn.addEventListener('click', function() {
      popup.style.display = 'none';
      document.body.style.overflow = '';
      document.body.classList.remove('klarna-popup-open');
    });
  });
  
  // Close on overlay click
  if (popup) {
    popup.addEventListener('click', function(e) {
      if (e.target === popup) {
        popup.style.display = 'none';
        document.body.style.overflow = '';
        document.body.classList.remove('klarna-popup-open');
      }
    });
  }
  
  // Update prices when variant changes
  const productForm = document.querySelector('#product-form-{{ product.id }}');
  if (productForm) {
    productForm.addEventListener('change', function(e) {
      if (e.target.name === 'id' || e.target.name.includes('options[')) {
        updateKlarnaPrices();
      }
    });
  }
  
  function updateKlarnaPrices() {
    const variantId = productForm.querySelector('[name="id"]').value;
    const productData = JSON.parse(document.querySelector('#ProductVariantsJSON-{{ product.id }}').textContent || '{}');
    const currentVariant = productData.variants?.find(variant => variant.id == variantId);
    
    if (currentVariant) {
      const installmentAmount = (currentVariant.price / 4 / 100).toFixed(2);
      const fullAmount = (currentVariant.price / 100).toFixed(2);
      
      // Update badge
      const badgeAmount = document.querySelector('#klarna-section-{{ section.id }} .klarna-payment-amount');
      if (badgeAmount) {
        badgeAmount.textContent = `4 interest-free payments of $${installmentAmount}`;
      }
      
      // Update popup amounts
      const popupAmounts = document.querySelectorAll('#klarna-popup-{{ section.id }} .klarna-option-price');
      if (popupAmounts[0]) popupAmounts[0].textContent = `$${fullAmount}`;
      if (popupAmounts[1]) popupAmounts[1].textContent = `$${installmentAmount}`;
      
      // Update installment amounts
      const installmentAmounts = document.querySelectorAll('#klarna-popup-{{ section.id }} .klarna-amount');
      installmentAmounts.forEach(function(amount) {
        amount.textContent = `$${installmentAmount}`;
      });
      
      // Update total
      const totalAmount = document.querySelector('#klarna-popup-{{ section.id }} .klarna-summary-item:last-child .klarna-summary-value');
      if (totalAmount) {
        totalAmount.textContent = `$${fullAmount}`;
      }
    }
  }
});
</script>

{% schema %}
{
  "name": "Klarna Payment Info",
  "class": "shopify-section-klarna-payment",
  "settings": [
    {
      "type": "checkbox",
      "id": "enable_klarna",
      "label": "Enable Klarna payment info",
      "default": true
    },
    {
      "type": "header",
      "content": "Badge Settings"
    },
    {
      "type": "html",
      "id": "custom_svg",
      "label": "Custom SVG Logo",
      "info": "Leave empty to use default KLARNA text"
    },
    {
      "type": "header",
      "content": "Popup Settings"
    },
    {
      "type": "html",
      "id": "popup_header_svg",
      "label": "Popup Header SVG",
      "info": "Leave empty to use default KLARNA text"
    },
    {
      "type": "text",
      "id": "popup_title",
      "label": "Popup Title",
      "default": "Buy now. Pay with Klarna at your own pace."
    },
    {
      "type": "text",
      "id": "popup_subtitle",
      "label": "Popup Subtitle",
      "default": "Get what you love, choose how you pay."
    },
    {
      "type": "textarea",
      "id": "disclaimer",
      "label": "Disclaimer Text",
      "default": "For Pay in 4, CA resident loans made or arranged pursuant to California financing law license. NMLS # 1353190."
    }
  ]
}
{% endschema %}

<style>
/* Klarna Payment Section Styles */
.klarna-payment-section {
  margin: 15px 0;
  font-family: Lato, sans-serif;
}

.klarna-badge-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.klarna-logo-default {
  background-color: rgb(255, 168, 205);
  padding: 6px 12px;
  border-radius: 6px;
  display: inline-block;
}

.klarna-text {
  color: #000000;
  font-weight: 700;
  font-size: 14px;
  font-family: Lato, sans-serif;
  line-height: 1;
}

.klarna-payment-text {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.klarna-payment-amount {
  color: #4a4a4a;
  font-size: 16px;
  font-weight: 400;
  font-family: Lato, sans-serif;
}

.klarna-learn-more {
  color: #4a4a4a !important;
  font-size: 16px;
  font-weight: 400;
  font-family: Lato, sans-serif;
  text-decoration: underline !important;
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  margin: 0;
}

.klarna-learn-more:hover {
  color: #4a4a4a !important;
  text-decoration: underline !important;
}

/* Popup Styles */
.klarna-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.klarna-popup {
  background: #ffffff;
  border-radius: 12px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.klarna-popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.klarna-popup-logo {
  display: flex;
  align-items: center;
}

.klarna-popup-title-text {
  font-family: Lato, sans-serif;
  font-weight: 700;
  font-size: 18px;
  color: #000000;
}

.klarna-popup-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.klarna-popup-close:hover {
  background-color: #f0f0f0;
}

.klarna-popup-content {
  padding: 24px;
}

.klarna-popup-main-title {
  font-family: Lato, sans-serif;
  font-weight: 700;
  font-size: 24px;
  color: #000000;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.klarna-popup-subtitle {
  font-family: Lato, sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #666666;
  margin: 0 0 24px 0;
}

.klarna-payment-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.klarna-option {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  position: relative;
}

.klarna-option-price {
  font-family: Lato, sans-serif;
  font-weight: 700;
  font-size: 28px;
  color: #000000;
  margin-bottom: 4px;
}

.klarna-option-label {
  font-family: Lato, sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  margin-bottom: 8px;
}

.klarna-option-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: #e8e3ff;
  color: #6b46c1;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  font-family: Lato, sans-serif;
}

.klarna-installment-schedule {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.klarna-installment {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 80px;
}

.klarna-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-bottom: 8px;
  position: relative;
}

.klarna-circle-1 { background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%); }
.klarna-circle-2 { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); }
.klarna-circle-3 { background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%); }
.klarna-circle-4 { background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%); }

.klarna-installment-text {
  text-align: center;
}

.klarna-amount {
  font-family: Lato, sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #000000;
  margin-bottom: 2px;
}

.klarna-timing {
  font-family: Lato, sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #666666;
}

.klarna-summary {
  display: flex;
  justify-content: space-between;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.klarna-summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.klarna-summary-label {
  font-family: Lato, sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #666666;
  margin-bottom: 4px;
}

.klarna-summary-value {
  font-family: Lato, sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #000000;
}

.klarna-disclaimer {
  font-family: Lato, sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #666666;
  margin: 0 0 24px 0;
  line-height: 1.4;
}

.klarna-how-it-works h3 {
  font-family: Lato, sans-serif;
  font-weight: 700;
  font-size: 20px;
  color: #000000;
  margin: 0 0 16px 0;
}

.klarna-step {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  align-items: flex-start;
}

.klarna-step-number {
  width: 24px;
  height: 24px;
  background: #000000;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  font-family: Lato, sans-serif;
  flex-shrink: 0;
  margin-top: 2px;
}

.klarna-step-content h4 {
  font-family: Lato, sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
  margin: 0 0 4px 0;
}

.klarna-step-content p {
  font-family: Lato, sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  margin: 0;
  line-height: 1.4;
}

.klarna-got-it-btn {
  width: 100%;
  background: #1a1a2e;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 16px;
  font-family: Lato, sans-serif;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  margin-top: 24px;
  transition: background-color 0.2s;
}

.klarna-got-it-btn:hover {
  background: #16213e;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .klarna-badge-container {
    gap: 12px;
  }

  .klarna-payment-amount,
  .klarna-learn-more {
    font-size: 15px;
  }

  .klarna-popup {
    margin: 10px;
    max-height: 95vh;
  }

  .klarna-popup-content {
    padding: 20px;
  }

  .klarna-popup-main-title {
    font-size: 22px;
  }

  .klarna-installment-schedule {
    gap: 12px;
  }

  .klarna-circle {
    width: 36px;
    height: 36px;
  }

  .klarna-summary {
    flex-direction: column;
    gap: 12px;
  }

  .klarna-summary-item {
    flex-direction: row;
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .klarna-badge-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .klarna-payment-text {
    gap: 6px;
  }

  .klarna-payment-amount,
  .klarna-learn-more {
    font-size: 14px;
  }

  .klarna-installment-schedule {
    gap: 8px;
  }

  .klarna-installment {
    min-width: 70px;
  }

  .klarna-circle {
    width: 32px;
    height: 32px;
  }

  .klarna-amount {
    font-size: 13px;
  }

  .klarna-timing {
    font-size: 11px;
  }
}
</style>
