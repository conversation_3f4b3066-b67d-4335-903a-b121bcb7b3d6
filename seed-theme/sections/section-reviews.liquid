{%- if section.blocks.size > 0 -%}
  <section class="reviews-section">
    <div class="reviews-container">
      <!-- Header Section -->
      <div class="reviews-header">
        {%- if section.settings.main_title != blank -%}
          <h2 class="reviews-main-title">{{ section.settings.main_title }}</h2>
        {%- endif -%}

        {%- if section.settings.subtitle != blank -%}
          <div class="reviews-subtitle-container">
            <p class="reviews-subtitle">{{ section.settings.subtitle }}</p>
            <div class="reviews-stars">
              {%- assign header_rating = section.settings.header_rating | default: 5 -%}
              {%- for i in (1..5) -%}
                <svg xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                  <path d="M8.88669 0.641217C9.10392 0.080722 9.89699 0.0807211 10.1142 0.641216L11.9813 5.45848C12.0747 5.69945 12.3004 5.86348 12.5585 5.87784L17.7169 6.1649C18.3171 6.1983 18.5622 6.95255 18.0963 7.33235L14.0917 10.5967C13.8914 10.7599 13.8052 11.0253 13.8712 11.2752L15.1923 16.2699C15.346 16.851 14.7044 17.3172 14.1992 16.9914L9.85718 14.1916C9.63998 14.0515 9.36093 14.0515 9.14373 14.1916L4.80173 16.9914C4.29653 17.3172 3.65493 16.851 3.80863 16.2699L5.12967 11.2752C5.19575 11.0253 5.10951 10.7599 4.9092 10.5967L0.904658 7.33236C0.438726 6.95255 0.683795 6.1983 1.28399 6.1649L6.44243 5.87784C6.70047 5.86348 6.92623 5.69946 7.01963 5.45849L8.88669 0.641217Z" fill="{%- if i <= header_rating -%}#FEC600{%- else -%}#E5E5E5{%- endif -%}"/>
                </svg>
              {%- endfor -%}
            </div>
          </div>
        {%- endif -%}
      </div>

      <!-- Reviews Cards -->
      <div class="reviews-cards-container">
        {%- for block in section.blocks -%}
          {%- if block.type == 'review_card' -%}
            <div class="review-card" {{ block.shopify_attributes }}>
              <!-- Stars -->
              <div class="review-rating">
              <div class="review-stars">
                {%- assign rating = block.settings.rating | default: 5 -%}
                {%- for i in (1..5) -%}
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="17" viewBox="0 0 18 17" fill="none">
                    <path d="M8.33776 0.990775C8.47127 0.646324 8.95864 0.646324 9.09214 0.990775L11.049 6.03965C11.1064 6.18774 11.2451 6.28854 11.4037 6.29736L16.8101 6.59823C17.179 6.61875 17.3296 7.08227 17.0433 7.31568L12.8462 10.7369C12.7231 10.8373 12.6701 11.0004 12.7107 11.1539L14.0952 16.3887C14.1897 16.7459 13.7954 17.0323 13.4849 16.8321L8.93418 13.8977C8.8007 13.8116 8.62921 13.8116 8.49573 13.8977L3.94497 16.8321C3.6345 17.0323 3.24021 16.7459 3.33467 16.3887L4.71921 11.1539C4.75982 11.0004 4.70683 10.8373 4.58373 10.7369L0.386655 7.31568C0.100317 7.08227 0.250925 6.61875 0.619771 6.59823L6.02623 6.29736C6.1848 6.28854 6.32354 6.18774 6.38094 6.03965L8.33776 0.990775Z" fill="{%- if i <= rating -%}#FEC600{%- else -%}#E5E5E5{%- endif -%}"/>
                  </svg>
                {%- endfor -%}
              </div>

              <!-- Date -->
              {%- if block.settings.date != blank -%}
                <div class="review-date">{{ block.settings.date }}</div>
              {%- endif -%}
               </div>
              <!-- Title -->
              {%- if block.settings.title != blank -%}
                <h3 class="review-title">{{ block.settings.title }}</h3>
              {%- endif -%}

              <!-- Description -->
              {%- if block.settings.description != blank -%}
                <p class="review-description">{{ block.settings.description }}</p>
              {%- endif -%}

              <!-- Author -->
              {%- if block.settings.author != blank -%}
                <div class="review-author">{{ block.settings.author }}</div>
              {%- endif -%}

              <!-- Image -->
              {%- if block.settings.image -%}
                <div class="review-image">
                  <img
                    src="{{ block.settings.image | image_url: width: 544 }}"
                    srcset="
                      {{ block.settings.image | image_url: width: 272 }} 272w,
                      {{ block.settings.image | image_url: width: 544 }} 544w
                    "
                    sizes="(max-width: 768px) 100vw, 272px"
                    alt="{{ block.settings.image.alt | default: block.settings.title | escape }}"
                    loading="lazy"
                    width="272"
                    height="176"
                  >
                </div>
              {%- endif -%}
            </div>
          {%- endif -%}
        {%- endfor -%}
      </div>

      <!-- Read All Reviews Button -->
      {%- if section.settings.show_read_all_button and section.settings.read_all_text != blank -%}
        {%- liquid
          assign button_url = '#'
          if section.settings.link_type == 'external' and section.settings.external_url != blank
            assign button_url = section.settings.external_url
          elsif section.settings.link_type == 'internal' and section.settings.read_all_url != blank
            assign button_url = section.settings.read_all_url
          endif
        -%}
        <div class="reviews-read-all">
          <a href="{{ button_url }}" class="read-all-button"{% if section.settings.link_type == 'external' %} target="_blank" rel="noopener noreferrer"{% endif %}>
            {{ section.settings.read_all_text }}
          </a>
        </div>
      {%- endif -%}
    </div>
  </section>
{%- endif -%}

<style>
  .reviews-section {
    background: #ffffff !important;
    width: 100vw !important;
    margin-left: calc(-50vw + 50%) !important;
    padding: 60px 0 5px;
    box-sizing: border-box;
    position: relative;
  }

  .reviews-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .reviews-header {
    text-align: center;
    margin-bottom: 50px;
  }

  .reviews-main-title {
    color: #4a4a4a;
    text-align: center;
    font-family: Merriweather, serif;
    font-size: 36px;
    font-style: normal;
    font-weight: 700;
    line-height: 50.4px;
    letter-spacing: 0.12px;
    margin: 0 0 16px 0;
  }

  .reviews-subtitle-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
  }

  .reviews-subtitle {
    color: #4a4a4a;
    text-align: center;
    font-family: Merriweather, serif;
    font-size: 36px;
    font-style: normal;
    font-weight: 700;
    line-height: 50.4px;
    letter-spacing: 0.12px;
    margin: 0;
  }

  .reviews-stars {
    display: flex;
    gap: 2px;
  }

  .reviews-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(272px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
  }

  .review-card {
    display: flex;
    width: 272px;
    padding: 16.733px;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
    border-radius: 10px;
    border: 1.046px solid #f5f5f5;
    background: #fff;
    box-shadow: 0 9.413px 12.55px 0 rgba(0, 158, 224, 0.08);
    box-sizing: border-box;
    justify-self: center;
  }

  .review-stars {
    display: flex;
    gap: 2px;
    margin-bottom: 8px;
  }
  .review-rating {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .review-date {
    color: #6a6c77;
    font-family: 'Open Sans', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
  }

  .review-title {
    color: #4a4a4a;
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: 0.061px;
    margin: 0 0 12px 0;
  }

  .review-description {
    color: #4a4a4a;
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    font-style: normal;
    line-height: 24px;
    margin: 0 0 5px 0;
    flex-grow: 1;
  }

  .review-author {
    color: #737373;
    font-family: 'Open Sans', sans-serif;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 17.192px;
  }

  .review-image {
    width: 100% !important;
    height: 176px !important;
    border-radius: 10px;
    overflow: hidden !important;
    margin-top: auto;
    flex-shrink: 0;
    min-height: 176px !important;
    max-height: 176px !important;
    position: relative;
  }

  .review-image img {
    width: 100% !important;
    height: 176px !important;
    object-fit: cover !important;
    object-position: center center !important;
    display: block !important;
    border-radius: 10px;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
  }

  .reviews-read-all {
    text-align: center;
    margin-top: 40px;
  }

  .read-all-button {
    display: inline-block;
    padding: 12px 32px;
    border: 2px solid #009ee0;
    border-radius: 41px;
    background: transparent;
    color: #009ee0;
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-weight: 700;
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .read-all-button:hover {
    background: #00aef8;
    color: #fff;
    text-decoration: none;
  }

  /* Mobile responsive */
  @media (max-width: 768px) {
    .reviews-section {
      padding: 40px 0 5px;
    }

    .reviews-container {
      padding: 0 16px;
    }

    .reviews-main-title,
    .reviews-subtitle {
      font-size: 28px;
      line-height: 36px;
    }

    .reviews-cards-container {
      display: flex;
      flex-direction: row;
      gap: 24px;
      overflow-x: auto;
      overflow-y: hidden;
      padding: 0 0 10px 0;
      scroll-snap-type: x mandatory;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: thin;
      scrollbar-color: #ccc transparent;
    }

    /* Webkit scrollbar styling for modern browsers */
    .reviews-cards-container::-webkit-scrollbar {
      height: 2px;
    }

    .reviews-cards-container::-webkit-scrollbar-track {
      background: transparent;
    }

    .reviews-cards-container::-webkit-scrollbar-thumb {
      background-color: #ccc;
      border-radius: 1px;
    }

    .reviews-cards-container::-webkit-scrollbar-thumb:hover {
      background-color: #999;
    }

    .review-card {
      min-width: 272px;
      max-width: 272px;
      width: 272px;
      height: auto;
      min-height: 400px;
      flex-shrink: 0;
      scroll-snap-align: start;
    }

    .review-image {
      width: 100%;
      height: 200px;
    }

    .review-image img {
      object-fit: cover;
      object-position: center center;
    }

    .reviews-subtitle-container {
      flex-direction: column;
      gap: 8px;
    }
  }

  @media (max-width: 480px) {
    .reviews-cards-container {
      gap: 20px;
      padding: 0 0 10px 0;
    }

    .review-card {
      min-width: 260px;
      max-width: 260px;
      width: 260px;
    }
  }

  @media (max-width: 480px) {
    .reviews-main-title,
    .reviews-subtitle {
      font-size: 24px;
      line-height: 32px;
    }

    .review-card {
      padding: 14px;
      gap: 5px;
    }

    .review-title {
      font-size: 15px;
      line-height: 22px;
    }

    .review-description {
      font-size: 14px;
      line-height: 24px;
    }

    .review-image {
      width: 100%;
      height: 180px;
    }

    .review-image img {
      object-fit: cover;
      object-position: center center;
    }
  }
</style>

{% schema %}
{
  "name": "Reviews Section",
  "class": "shopify-section-reviews",
  "settings": [
    {
      "type": "text",
      "id": "main_title",
      "label": "Main Title",
      "default": "Truly Custom Insoles - #1 in the USA"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Over 13,973 Reviews"
    },
    {
      "type": "range",
      "id": "header_rating",
      "label": "Header Star Rating",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 5
    },
    {
      "type": "checkbox",
      "id": "show_read_all_button",
      "label": "Show Read All Reviews Button",
      "default": true
    },
    {
      "type": "text",
      "id": "read_all_text",
      "label": "Read All Button Text",
      "default": "READ ALL REVIEWS (13,973)"
    },
    {
      "type": "header",
      "content": "Button Link Settings"
    },
    {
      "type": "select",
      "id": "link_type",
      "label": "Link Type",
      "options": [
        {
          "value": "internal",
          "label": "Internal Page"
        },
        {
          "value": "external",
          "label": "External URL"
        }
      ],
      "default": "internal"
    },
    {
      "type": "url",
      "id": "read_all_url",
      "label": "Internal Page URL",
      "info": "Select a page from your store",
      "visible_if": "{{ section.settings.link_type == 'internal' }}"
    },
    {
      "type": "text",
      "id": "external_url",
      "label": "External URL",
      "info": "Enter a full URL (e.g., https://example.com)",
      "placeholder": "https://",
      "visible_if": "{{ section.settings.link_type == 'external' }}"
    }
  ],
  "blocks": [
    {
      "type": "review_card",
      "name": "Review Card",
      "settings": [
        {
          "type": "range",
          "id": "rating",
          "label": "Star Rating",
          "min": 1,
          "max": 5,
          "step": 1,
          "default": 5
        },
        {
          "type": "text",
          "id": "date",
          "label": "Review Date",
          "default": "06/25/25"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Review Title",
          "default": "Fit perfectly"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Review Description",
          "default": "I ordered my orthotics on line. It was quick and easy. I had old orthotics and sent pictures. They made adjustments to the new ones to fit me. The new ones fit perfectly when I received them. Great job. I will use Ustep again."
        },
        {
          "type": "text",
          "id": "author",
          "label": "Review Author",
          "default": "Paul Z."
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Review Image"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Reviews Section",
      "blocks": [
        {
          "type": "review_card",
          "settings": {
            "rating": 5,
            "date": "06/25/25",
            "title": "Fit perfectly",
            "description": "I ordered my orthotics on line. It was quick and easy. I had old orthotics and sent pictures. They made adjustments to the new ones to fit me. The new ones fit perfectly when I received them. Great job. I will use Ustep again.",
            "author": "Paul Z."
          }
        },
        {
          "type": "review_card",
          "settings": {
            "rating": 5,
            "date": "05/28/25",
            "title": "It really works!",
            "description": "I have been dealing with planters fasciitis for over seven months. I literally put these in and within the first day I could feel the difference and now I'm on the second day only and literally have no pain hardly at all.",
            "author": "Nicole"
          }
        },
        {
          "type": "review_card",
          "settings": {
            "rating": 5,
            "date": "02/27/25",
            "title": "Upstep orthotics are the cure—my plantar fasciitis is gone!!",
            "description": "spent over seven hundred dollars and went nearly a year attempting to self-treat with stretch bands, boots, socks, tape, terrible shoe inserts, NSAIDS... Only after using Upstep orthotics have I obtained my life back with full foot pain relief.",
            "author": "Sam P."
          }
        },
        {
          "type": "review_card",
          "settings": {
            "rating": 5,
            "date": "12/16/24",
            "title": "Back on my feet",
            "description": "These insoles have changed the game for me. Having high arches I put a lot of pressure on my heels but now the balance is just right. Thanks again !",
            "author": "Brandon"
          }
        }
      ]
    }
  ]
}
{% endschema %}
