{%- if section.blocks.size > 0 -%}
  <section class="sports-orthotics-section" style="background: #FFFFFF; width: 100vw; margin-left: calc(-50vw + 50%); padding: 60px 0;">
    <div class="sports-orthotics-container" style="max-width: calc(100vw - 80px); margin: 0 auto;">

      {%- if section.settings.title != blank -%}
        <h2 class="sports-orthotics-title">{{ section.settings.title }}</h2>
      {%- endif -%}

      <div class="sports-orthotics-nav-wrapper">
        <button class="sports-orthotics-nav sports-orthotics-nav-left" aria-label="Previous cards">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <button class="sports-orthotics-nav sports-orthotics-nav-right" aria-label="Next cards">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>

      <div class="sports-orthotics-cards-container">
          {%- for block in section.blocks -%}
            {%- if block.type == 'sports_card' -%}
              {%- if block.settings.product_url != blank -%}
                <a href="{{ block.settings.product_url }}" class="sports-orthotics-card" {{ block.shopify_attributes }}>
              {%- else -%}
                <div class="sports-orthotics-card" {{ block.shopify_attributes }}>
              {%- endif -%}
                <div class="sports-orthotics-card-image">
                  {%- if block.settings.image -%}
                    <img
                      src="{{ block.settings.image | image_url: width: 331 }}"
                      srcset="{{ block.settings.image | image_url: width: 331 }} 331w,
                              {{ block.settings.image | image_url: width: 662 }} 662w"
                      sizes="331px"
                      alt="{{ block.settings.image.alt | default: block.settings.title | escape }}"
                      loading="lazy"
                      width="331"
                      height="221"
                    >
                  {%- else -%}
                    <div class="sports-orthotics-placeholder" style="background-color: #BEEBFC;"></div>
                  {%- endif -%}
                </div>

                <div class="sports-orthotics-card-content">
                  <div class="sports-orthotics-card-info">
                    {%- if block.settings.icon_svg != blank -%}
                      <div class="sports-orthotics-icon">{{ block.settings.icon_svg }}</div>
                    {%- else -%}
                      <div class="sports-orthotics-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <circle cx="12" cy="12" r="10" stroke="#5B6971" stroke-width="2"/>
                          <path d="M12 6v6l4 2" stroke="#5B6971" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </div>
                    {%- endif -%}

                    {%- if block.settings.title != blank -%}
                      <span class="sports-orthotics-card-title">{{ block.settings.title }}</span>
                    {%- endif -%}
                  </div>
                </div>
              {%- if block.settings.product_url != blank -%}
                </a>
              {%- else -%}
                </div>
              {%- endif -%}
            {%- endif -%}
          {%- endfor -%}
        </div>
      
      {%- if section.settings.show_button and section.settings.button_text != blank and section.settings.button_url != blank -%}
        <div class="sports-orthotics-button-wrapper">
          <a href="{{ section.settings.button_url }}" class="sports-orthotics-button">
            {{ section.settings.button_text }}
          </a>
        </div>
      {%- endif -%}
    </div>
  </section>

  <style>
    .sports-orthotics-section {
      position: relative;
    }

    .sports-orthotics-title {
      color: #4A4A4A;
      text-align: center;
      font-family: Merriweather, serif;
      font-size: 36px;
      font-style: normal;
      font-weight: 700;
      line-height: 50px; /* 95% */
      letter-spacing: 0.2px;
    }

    .sports-orthotics-nav-wrapper {
      display: flex;
      justify-content: flex-end;
      gap: 15px;
    }

    .sports-orthotics-cards-container {
      display: flex;
      gap: 20px;
      overflow: hidden;
      scroll-behavior: smooth;
      scrollbar-width: none;
      -ms-overflow-style: none;
      margin-bottom: 50px;
      padding: 10px 15px 5px 15px;
      width: calc(100% - 30px);
      margin-left: 15px;
      margin-right: 15px;
      box-sizing: content-box;
    }

    .sports-orthotics-cards-container::-webkit-scrollbar {
      display: none;
    }

    .sports-orthotics-card {
      flex: 0 0 calc((100% - 60px) / 4);
      max-width: calc((100% - 60px) / 4);
      height: 320px;
      background: #FFFFFF;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      text-decoration: none;
      color: inherit;
      cursor: pointer;
      display: block;
    }

    .sports-orthotics-card:hover {
      text-decoration: none;
      color: inherit;
    }

    /* Адаптивные размеры карточек */
    @media (min-width: 1200px) {
      .sports-orthotics-card {
        flex: 0 0 calc((100% - 60px) / 4);
        max-width: calc((100% - 60px) / 4);
      }
    }

    @media (max-width: 1199px) and (min-width: 900px) {
      .sports-orthotics-card {
        flex: 0 0 calc((100% - 40px) / 3);
        max-width: calc((100% - 40px) / 3);
      }

      .sports-orthotics-cards-container {
        width: calc(100% - 30px);
      }
    }

    .sports-orthotics-card-image {
      width: 100%;
      height: 221px;
      overflow: hidden;
      position: relative;
    }

    .sports-orthotics-card-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .sports-orthotics-placeholder {
      width: 100%;
      height: 100%;
      background-color: #BEEBFC;
    }

    .sports-orthotics-card-content {
      padding: 20px;
      height: calc(335px - 221px - 40px);
      display: flex;
      align-items: center;
    }

    .sports-orthotics-card-info {
      display: flex;
      align-items: center;
      gap: 12px;
      width: 100%;
      margin-top: 25px;
    }

    .sports-orthotics-icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sports-orthotics-icon svg {
      width: 100%;
      height: 100%;
      color: #5B6971;
    }

    .sports-orthotics-card-title {
      color: #4a4a4a;
      font-family: Lato, sans-serif;
      font-size: 15px;
      font-style: normal;
      font-weight: 700;
      line-height: 24px; /* 171.429% */
      letter-spacing: 0.12px;
      flex: 1;
    }

    .sports-orthotics-nav {
      background: #FFFFFF;
      border: 1px solid #E5E5E5;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      min-width: 40px;
      min-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #5B6971;
      flex-shrink: 0;
      padding: 0;
      margin: 0;
      outline: none;
      box-sizing: border-box;
      position: relative;
      overflow: hidden;
      transition: none;
    }

    .sports-orthotics-nav::before,
    .sports-orthotics-nav::after {
      display: none !important;
      content: none !important;
    }

    .sports-orthotics-nav:hover::before,
    .sports-orthotics-nav:hover::after,
    .sports-orthotics-nav:focus::before,
    .sports-orthotics-nav:focus::after,
    .sports-orthotics-nav:active::before,
    .sports-orthotics-nav:active::after {
      display: none !important;
      content: none !important;
    }

    .sports-orthotics-nav:hover {
      color: #5B6971 !important;
      border-color: #E5E5E5;
      background: #F8F9FA;
    }

    .sports-orthotics-nav:hover svg {
      color: #5B6971 !important;
    }

    .sports-orthotics-nav:hover svg path {
      stroke: #5B6971 !important;
    }

    .sports-orthotics-nav:focus {
      outline: none;
      box-shadow: none;
      background: #FFFFFF;
    }

    .sports-orthotics-nav:disabled {
      opacity: 0.3;
      cursor: not-allowed;
      color: #CCCCCC;
      border-color: #E5E5E5;
      background: #FFFFFF;
    }

    .sports-orthotics-nav svg {
      width: 16px;
      height: 16px;
      pointer-events: none;
      color: #5B6971 !important;
    }

    .sports-orthotics-nav svg path {
      stroke: #5B6971 !important;
    }

    /* Убираем все возможные эффекты с кнопок */
    .sports-orthotics-nav,
    .sports-orthotics-nav:hover,
    .sports-orthotics-nav:focus,
    .sports-orthotics-nav:active,
    .sports-orthotics-nav:visited {
      transition: none !important;
      transform: none !important;
      box-shadow: none !important;
      text-decoration: none !important;
    }

    .sports-orthotics-nav * {
      transition: none !important;
      transform: none !important;
    }

    .sports-orthotics-button-wrapper {
      text-align: center;
    }

    .sports-orthotics-button {
      display: inline-block;
      width: 336px;
      height: 51px;
      border-radius: 41px;
      background: #00AEF8;
      box-shadow: 0 4px 16px 0 rgba(0, 158, 224, 0.40);
      color: #FFFFFF;
      text-align: center;
      font-family: Lato, sans-serif;
      font-size: 16px;
      font-style: normal;
      font-weight: 900;
      line-height: 51px;
      text-decoration: none;
      text-transform: uppercase;
      transition: background-color 0.3s ease;
      border: none;
      cursor: pointer;
    }

    .sports-orthotics-button:hover {
      background: #33BFFF;
      color: #FFFFFF;
      text-decoration: none;
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
      .sports-orthotics-title {
        font-size: 22px;
        line-height: 30px;
        text-align: center;

      }

      .sports-orthotics-nav-wrapper {
        display: none;
      }

      .sports-orthotics-cards-container {
        margin-bottom: 30px;
        overflow-x: auto;
        overflow-y: hidden;
        padding: 10px 20px 15px 20px;
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-right: 0;
        -webkit-overflow-scrolling: touch;
        scroll-snap-type: x mandatory;
        box-sizing: border-box;
        scrollbar-width: thin;
        scrollbar-color: #ccc transparent;
      }

      /* Webkit scrollbar styling for modern browsers */
      .sports-orthotics-cards-container::-webkit-scrollbar {
        height: 2px;
      }

      .sports-orthotics-cards-container::-webkit-scrollbar-track {
        background: transparent;
      }

      .sports-orthotics-cards-container::-webkit-scrollbar-thumb {
        background-color: #ccc;
        border-radius: 1px;
      }

      .sports-orthotics-cards-container::-webkit-scrollbar-thumb:hover {
        background-color: #999;
      }

      .sports-orthotics-card {
        flex: 0 0 calc(100vw - 40px);
        max-width: calc(100vw - 40px);
        scroll-snap-align: center;
      }

      .sports-orthotics-button {
        width: min(336px, calc(100% - 40px));
        font-size: 16px;
      }
    }

    @media (max-width: 480px) {
      .sports-orthotics-title {
        font-size: 22px;
        line-height: 30px;
      }

      .sports-orthotics-cards-container {
        padding: 10px 16px 15px 16px;
      }

      .sports-orthotics-card {
        flex: 0 0 260px;
        max-width: 260px;
        height: 300px;
      }

      .sports-orthotics-card-image {
        height: 180px;
      }

      .sports-orthotics-card-content {
        height: calc(300px - 180px - 40px);
      }
    }
  </style>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const container = document.querySelector('.sports-orthotics-cards-container');
      const leftBtn = document.querySelector('.sports-orthotics-nav-left');
      const rightBtn = document.querySelector('.sports-orthotics-nav-right');

      if (!container || !leftBtn || !rightBtn) return;

      let currentIndex = 0;
      const cards = container.querySelectorAll('.sports-orthotics-card');
      const totalCards = cards.length;

      function getVisibleCards() {
        if (window.innerWidth >= 1200) {
          return 4; // 4 карточки на больших экранах
        } else if (window.innerWidth >= 900) {
          return 3; // 3 карточки на средних экранах
        } else {
          return 1; // 1 карточка на мобильных
        }
      }

      function getCardWidth() {
        if (cards.length > 0) {
          return cards[0].offsetWidth + 20; // ширина карточки + gap
        }
        return 331 + 20;
      }

      function updateButtons() {
        const visibleCards = getVisibleCards();

        // Скрываем кнопки на мобильных
        if (window.innerWidth <= 768) {
          leftBtn.style.display = 'none';
          rightBtn.style.display = 'none';
          return;
        } else {
          leftBtn.style.display = 'flex';
          rightBtn.style.display = 'flex';
        }

        leftBtn.disabled = currentIndex <= 0;
        rightBtn.disabled = currentIndex >= totalCards - visibleCards;
      }

      function scrollCards(direction) {
        const visibleCards = getVisibleCards();
        const cardWidth = getCardWidth();

        if (direction === 'left' && currentIndex > 0) {
          currentIndex--;
        } else if (direction === 'right' && currentIndex < totalCards - visibleCards) {
          currentIndex++;
        }

        const targetScroll = currentIndex * cardWidth;

        container.scrollTo({
          left: targetScroll,
          behavior: 'smooth'
        });

        updateButtons();
      }

      leftBtn.addEventListener('click', () => scrollCards('left'));
      rightBtn.addEventListener('click', () => scrollCards('right'));

      window.addEventListener('resize', () => {
        // Пересчитываем позицию при изменении размера окна
        const visibleCards = getVisibleCards();
        if (currentIndex >= totalCards - visibleCards) {
          currentIndex = Math.max(0, totalCards - visibleCards);
        }
        const cardWidth = getCardWidth();
        const targetScroll = currentIndex * cardWidth;
        container.scrollTo({
          left: targetScroll,
          behavior: 'smooth'
        });
        updateButtons();
      });

      // Initial button state
      updateButtons();
    });
  </script>
{%- endif -%}

{% schema %}
{
  "name": "Sports Orthotics Cards",
  "class": "shopify-section-sports-orthotics",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Discover Our Sports Orthotics"
    },
    {
      "type": "checkbox",
      "id": "show_button",
      "label": "Show Button",
      "default": true
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "SHOP FOR SPORT"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button Link"
    }
  ],
  "blocks": [
    {
      "type": "sports_card",
      "name": "Sports Card",
      "limit": 10,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Card Image"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Card Title",
          "default": "Multi-Sports Custom Orthotics"
        },
        {
          "type": "html",
          "id": "icon_svg",
          "label": "Custom Icon SVG",
          "info": "Paste your custom SVG code here. Leave empty to use default icon."
        },
        {
          "type": "url",
          "id": "product_url",
          "label": "Product Link",
          "info": "Link to the product page"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Sports Orthotics Cards",
      "settings": {
        "title": "Athletic Custom Orthotics for Active Lifestyles",
        "show_button": true,
        "button_text": "SHOP FOR SPORT"
      },
      "blocks": [
        {
          "type": "sports_card",
          "settings": {
            "title": "Multi-Sports Custom Orthotics"
          }
        },
        {
          "type": "sports_card",
          "settings": {
            "title": "Running Custom Orthotics"
          }
        },
        {
          "type": "sports_card",
          "settings": {
            "title": "Fitness Walking & Hiking Custom Orthotics"
          }
        },
        {
          "type": "sports_card",
          "settings": {
            "title": "Gym Custom Orthotics"
          }
        }
      ]
    }
  ]
}
{% endschema %}
